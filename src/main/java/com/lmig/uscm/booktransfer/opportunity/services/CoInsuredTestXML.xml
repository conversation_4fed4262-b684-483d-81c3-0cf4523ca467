<ACORD>
  <BookTransferMeta>
    <creation>
      <type>xmlExtraction</type>
      <origin>AQE</origin>
    </creation>
  </BookTransferMeta>
  <SignonRq>
    <SignonPswd>
      <CustId>
        <SPName>AMSServices.com</SPName>
        <CustPermId/>
        <CustLoginId><EMAIL></CustLoginId>
      </CustId>
      <CustPswd>
        <EncryptionTypeCd>NONE</EncryptionTypeCd>
        <Pswd/>
      </CustPswd>
    </SignonPswd>
    <ClientDt>7/11/2023 12:52 PM</ClientDt>
    <CustLangPref>en-US</CustLangPref>
    <ClientApp>
      <Org>AMS Services</Org>
      <Name>Transit</Name>
      <Version>V2.5.5</Version>
    </ClientApp>
    <ProxyClient>
      <Org>BCFTech</Org>
      <Name>TransmitXML</Name>
      <Version>V1.00</Version>
    </ProxyClient>
  </SignonRq>
  <InsuranceSvcRq BypassScrubbing="SkipNaming">
    <RqUID>19191156-5738-4483-8E24-16B4CA8C6352</RqUID>
    <CommlAutoPolicyQuoteInqRq>
      <RqUID>CC11369C-EEA7-4933-A5BE-BC0D5B37E0A9</RqUID>
      <TransactionRequestDt>2023-07-11</TransactionRequestDt>
      <TransactionEffectiveDt>2023-05-03</TransactionEffectiveDt>
      <CurCd>USD</CurCd>
      <Producer>
        <ProducerInfo>
          <ContractNumber>92322023</ContractNumber>
          <ProducerRoleCd>Agency</ProducerRoleCd>
        </ProducerInfo>
      </Producer>
      <InsuredOrPrincipal>
        <ItemIdInfo>
          <InsurerId>3446F5146</InsurerId>
          <OtherIdentifier>
            <OtherIdTypeCd>Insured</OtherIdTypeCd>
            <OtherId>APPLIED</OtherId>
          </OtherIdentifier>
        </ItemIdInfo>
        <GeneralPartyInfo>
          <NameInfo>
            <PersonName>
              <Surname>SALLY</Surname>
              <GivenName>BRANDSTETTER</GivenName>
            </PersonName>
            <CommlName>
              <CommercialName>BRANDSTETTER SALLY</CommercialName>
            </CommlName>
            <LegalEntityCd>CP</LegalEntityCd>
          </NameInfo>
          <Addr>
          <AddrTypeCd>MailingAddress</AddrTypeCd>
          <Addr1>906 NEW YORK ST </Addr1>
          <City>LONGVIEW</City>
          <StateProvCd>WA</StateProvCd>
          <PostalCode>986324132</PostalCode>
        </Addr>
        <Communications>
          <PhoneInfo>
            <PhoneTypeCd>Phone</PhoneTypeCd>
            <CommunicationUseCd>Day</CommunicationUseCd>
            <PhoneNumber>******-8675309</PhoneNumber>
          </PhoneInfo>
        </Communications>
      </GeneralPartyInfo>
      <InsuredOrPrincipalInfo>
        <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
        <BusinessInfo>
          <SICCd>8712</SICCd>
          <NAICSCd>541310</NAICSCd>
          <NumEmployees>5</NumEmployees>
        </BusinessInfo>
      </InsuredOrPrincipalInfo>
    </InsuredOrPrincipal>
    <CommlPolicy id="PolicyLevel_M36">
      <PolicyNumber>UMC0002241</PolicyNumber>
      <LOBCd>AUTOB</LOBCd>
      <LOBSubCd>NPO</LOBSubCd>
      <NAICCd>99999</NAICCd>
      <ControllingStateProvCd>OR</ControllingStateProvCd>
      <ContractTerm>
        <EffectiveDt>2025-05-03</EffectiveDt>
        <ExpirationDt>2026-05-03</ExpirationDt>
        <DurationPeriod>
          <NumUnits>12</NumUnits>
          <UnitMeasurementCd>MON</UnitMeasurementCd>
        </DurationPeriod>
      </ContractTerm>
      <BillingMethodCd>CPB</BillingMethodCd>
      <CurrentTermAmt>
        <Amt>2900.00</Amt>
      </CurrentTermAmt>
      <LanguageCd>E</LanguageCd>
      <RateEffectiveDt>2023-05-03</RateEffectiveDt>
      <PaymentOption>
        <PaymentPlanCd>OT</PaymentPlanCd>
      </PaymentOption>
      <CommlPolicySupplement>
        <AuditInd>1</AuditInd>
        <NumEmployees>5</NumEmployees>
        <AuditFrequencyCd>XP</AuditFrequencyCd>
      </CommlPolicySupplement>
    </CommlPolicy>
    <PaymentOption>
      <SecondPersonHasData>1</SecondPersonHasData>
      <com.safeco_MonthlyPaymentPlan>N</com.safeco_MonthlyPaymentPlan>
      <com.safeco_RecurringPaymentInfo>
        <com.safeco_TokenizedAccountNumber>5843681698134472056</com.safeco_TokenizedAccountNumber>
        <ElectronicFundsTransfer>
          <FromAcct>
            <BankInfo>
              <AccountNumberId isMasked="true">**********5678</AccountNumberId>
              <BankId>*********</BankId>
            </BankInfo>
            <MiscParty>
              <Surname>BOLEN</Surname>
              <GivenName>RICHARD</GivenName>
            </MiscParty>
          </FromAcct>
        </ElectronicFundsTransfer>
        <com.safeco_InstrumentId>***********</com.safeco_InstrumentId>
        <AcctTypeCd>
          <MethodOfPayment>Checking</MethodOfPayment>
        </AcctTypeCd>
      </com.safeco_RecurringPaymentInfo>
      <CommercialName>RICHARD BOLEN ENTERPRISES</CommercialName>
      <PaymentPlanCd>E</PaymentPlanCd>
      <DayMonthDue>11</DayMonthDue>
      <MethodPaymentCd>EFT</MethodPaymentCd>
    </PaymentOption>
  </CommlAutoPolicyQuoteInqRq>
</InsuranceSvcRq>
</ACORD>
